'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import UnifiedCard from '@/components/ui/UnifiedCard';
import { ALL_TOOLS } from '@/data/tools';
import { ALL_CALCULATORS } from '@/data/calculators';

export default function NavigationTest() {
  const router = useRouter();
  const [logs, setLogs] = useState<string[]>([]);
  
  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // Test data
  const testTool = ALL_TOOLS.find(tool => tool.id === 'pdf-to-word');
  const testCalculator = ALL_CALCULATORS.find(calc => calc.id === 'bmi');

  const handleDirectNavigation = (path: string) => {
    addLog(`Attempting direct navigation to: ${path}`);
    try {
      router.push(path);
      addLog(`Direct navigation successful`);
    } catch (error) {
      addLog(`Direct navigation failed: ${error}`);
    }
  };

  return (
    <div className="p-8 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">Navigation Test</h1>
      
      {/* Test Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div>
          <h2 className="text-xl font-semibold mb-4">Test Tool Card</h2>
          {testTool && (
            <UnifiedCard
              id={testTool.id}
              title={testTool.title}
              description={testTool.description}
              icon={testTool.icon}
              type="tool"
              category={testTool.category}
              inputFormat={testTool.inputFormat}
              outputFormat={testTool.outputFormat}
              popular={testTool.popular}
              variant="enhanced"
            />
          )}
        </div>
        
        <div>
          <h2 className="text-xl font-semibold mb-4">Test Calculator Card</h2>
          {testCalculator && (
            <UnifiedCard
              id={testCalculator.id}
              title={testCalculator.title}
              description={testCalculator.description}
              icon={testCalculator.icon}
              type="calculator"
              category={testCalculator.category}
              popular={testCalculator.popular}
              variant="enhanced"
            />
          )}
        </div>
      </div>

      {/* Direct Navigation Buttons */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Direct Navigation Test</h2>
        <div className="flex gap-4 flex-wrap">
          <button
            onClick={() => handleDirectNavigation('/tools/pdf-to-word')}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Navigate to PDF to Word
          </button>
          <button
            onClick={() => handleDirectNavigation('/calculators/bmi')}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Navigate to BMI Calculator
          </button>
          <button
            onClick={() => handleDirectNavigation('/tools')}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            Navigate to All Tools
          </button>
          <button
            onClick={() => handleDirectNavigation('/calculators/all')}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            Navigate to All Calculators
          </button>
        </div>
      </div>

      {/* Logs */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Navigation Logs</h2>
        <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg max-h-64 overflow-y-auto">
          {logs.length === 0 ? (
            <p className="text-gray-500">No logs yet. Try clicking on cards or buttons above.</p>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="text-sm font-mono mb-1">
                {log}
              </div>
            ))
          )}
        </div>
        <button
          onClick={() => setLogs([])}
          className="mt-2 px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
        >
          Clear Logs
        </button>
      </div>
    </div>
  );
}
